import { createRouter, createWebHistory } from 'vue-router';
import Login from '../views/Login.vue';
import FoodList from '../views/FoodList.vue';
import Me from '../views/Me.vue';
import Shop from '../views/Shop.vue';

const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/food-list',
    name: 'FoodList',
    component: FoodList
  },
  {
    path: '/me',
    name: 'Me',
    component: Me
  },
  {
    path: '/shop',
    name: 'Shop',
    component: Shop
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

export default router;
