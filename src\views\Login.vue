<template>
  <div class="login-container">
    <h2>登录</h2>
    <el-form :model="loginForm" @submit.prevent="handleLogin">
      <el-form-item label="用户名">
        <el-input v-model="loginForm.username" placeholder="用户名" ></el-input>
      </el-form-item>
      <el-form-item label="密码">
        <el-input
          v-model="loginForm.password"
          type="password"
          placeholder="密码"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" native-type="submit">登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

export default {
  setup() {
    const loginForm = ref({
      username: '',
      password: ''
    });
    const router = useRouter();

    const handleLogin = () => {
      // 验证特定账户名和密码
      if (loginForm.value.username === 'admin' && loginForm.value.password === '123456') {
        console.log('登录成功');
        // 登录成功，跳转到美食列表页面
        router.push({ name: 'FoodList' });
      } else {
        alert('用户名或密码错误！请使用admin/admin登录');
      }
    };

    return {
      loginForm,
      handleLogin
    };
  }
};
</script>

<style scoped>
.login-container {
  background-color: #e6f0f5;
  border-radius: 10px;
  padding: 20px;
  width: 300px;
  margin: 100px auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  width: 60px !important;
  text-align: right;
  padding-right: 10px;
}

:deep(.el-form-item__content) {
  margin-left: 60px !important;
}

:deep(.el-input) {
  width: 100%;
}

button {
  margin: auto;
  width: 100%;
}
</style>
