<template>
  <div class="carousel-container">
    <el-carousel height="300px" trigger="click"  :autoplay="true" interval="3000">
      <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
        <div class="carousel-content">
          <img :src="item.image" alt="Carousel Image" class="carousel-image" />
          <div class="carousel-caption">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import { ref } from 'vue';

export default {
  setup() {
    const carouselItems = ref([
      {
        image: '/src/asserts/banner1.png',
        title: '',
        description: '',
      },
      {
        image: '/src/asserts/banner2.png',
        title: '',
        description: '',
      },
      {
        image: '/src/asserts/banner3.png',
        title: '',
        description: '',
      },
	{
        image: '/src/asserts/banner4.png',
        title: '',
        description: '',
      },
    ]);

    return {
      carouselItems,
    };
  },
};
</script>

<style scoped>
.carousel-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.carousel-content {
  position: relative;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 10px;
  text-align: center;
}

.carousel-caption h3 {
  margin: 0;
  font-size: 1.5em;
}

.carousel-caption p {
  margin: 5px 0 0;
  font-size: 1em;
}
</style>

