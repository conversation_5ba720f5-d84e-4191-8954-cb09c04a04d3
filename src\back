<template>
  <div id="app">
    <router-view></router-view>
    <div class="nav-buttons">
      <el-button @click="goToPage('Login')">首页</el-button>
      <el-button @click="goToPage('FoodList')">购物</el-button>
      <el-button @click="goToPage('Me')">我的</el-button>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';

export default {
  setup() {
    const router = useRouter();

    const goToPage = (name) => {
      router.push({ name });
    };

    return {
      goToPage
    };
  }
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

.nav-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 10px;
  background-color: #f0f0f0;
}

@media (max-width: 768px) {
  .login-container {
    width: 90%;
    margin: 50px auto;
  }

  .food-list-container,
  .me-container {
    padding: 10px;
  }

  .nav-buttons {
    padding: 5px;
  }
}
</style>
